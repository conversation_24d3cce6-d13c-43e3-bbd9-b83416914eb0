# 付款审批流程优化总结

## 问题分析

### 1. 驳回页面提交没反应
**问题原因：**
- `flowApvContractPayInfoReEdit.vue` 页面提交时缺少 `processDefinitionKey` 字段
- 后端 `audit` 方法需要通过 `processDefinitionKey` 来判断是采购流程还是安装流程

**解决方案：**
- 在重新编辑页面的提交参数中添加 `processDefinitionKey` 字段
- 从 `props.processDefinitionId` 中提取流程定义Key（格式：`key:version:id`）

### 2. 已办理任务查看详情时审批意见和按钮仍存在
**问题原因：**
- 审核页面没有根据任务状态动态控制审批区域的显示
- 已办理任务不应该显示审批意见和提交按钮

**解决方案：**
- 添加 `showAuditSection` 计算属性，根据 `taskId` 是否存在判断是否显示审批区域
- 已办理任务只显示关闭按钮，待办理任务显示完整的审批区域

### 3. 采购和安装流程的可驳回节点区别
**问题原因：**
- 当前代码中所有流程使用相同的可驳回节点列表
- 采购流程(type=1)和安装流程(type=2)的审批节点不同

**解决方案：**
- 分别定义采购和安装流程的可驳回节点列表
- 根据流程定义ID或type字段动态选择对应的节点列表

## 优化内容

### 1. 修复驳回页面提交问题
**文件：** `src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoReEdit.vue`

**修改内容：**
```javascript
// 获取流程定义Key - 从processDefinitionId中提取
let processDefinitionKey = '';
if (props.processDefinitionId) {
    const parts = props.processDefinitionId.split(':');
    if (parts.length >= 1) {
        processDefinitionKey = parts[0];
    }
}

let params = Object.assign({}, payInfoData, {
  flow: {
    // ... 其他字段
    processDefinitionKey: processDefinitionKey, // 添加流程定义Key
    // ... 其他字段
  }
});
```

### 2. 动态控制审批区域显示
**文件：** 
- `src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoAudit.vue`
- `src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoFinanceAudit.vue`

**修改内容：**
```javascript
// 控制审批区域显示 - 根据任务状态判断
const showAuditSection = computed(() => {
  // 如果没有taskId，说明是查看已办理任务，不显示审批区域
  return props.taskId && props.taskId !== null && props.taskId !== '';
});
```

**模板修改：**
```vue
<!-- 审批意见区域 - 根据任务状态动态显示 -->
<template v-if="showAuditSection">
  <el-divider content-position="left">审批意见</el-divider>
  <!-- 审批表单和提交按钮 -->
</template>

<!-- 已办理任务只显示关闭按钮 -->
<template v-else>
  <el-form-item style="margin-bottom: 0;">
    <el-button icon="Close" @click="handleCancel">关闭</el-button>
  </el-form-item>
</template>
```

### 3. 区分采购和安装流程的可驳回节点
**文件：** `src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoAudit.vue`

**修改内容：**
```javascript
// 采购流程可驳回的节点列表 (type=1)
const mpCanBackTaskKeyList = [
  {label: '合同责任人',     value: 'contract_resp',        sort: 1},
  {label: '供应链中心',     value: 'supply_chain_center',  sort: 2},
  {label: '分管高管',       value: 'senior',               sort: 3},
  {label: '财务经理',       value: 'fpd',                  sort: 4},
];

// 安装流程可驳回的节点列表 (type=2)
const iwpCanBackTaskKeyList = [
  {label: '安装合同责任人',   value: 'install_contract_holder', sort: 1},
  {label: '实施服务中心主任', value: 'isc_director',           sort: 2},
  {label: '常务副总',        value: 'ev',                     sort: 3},
  {label: '总经理',          value: 'gm',                     sort: 4},
  {label: '财务经理',        value: 'fpd',                    sort: 5},
];

// 可驳回的节点（动态计算）
const computeTaskKeyList = computed(() => {
  // 根据流程类型选择对应的可驳回节点列表
  let canBackTaskKeyList = [];
  
  if (props.processDefinitionId && props.processDefinitionId.includes('mp_payment')) {
    // 物资采购合同付款流程
    canBackTaskKeyList = mpCanBackTaskKeyList;
  } else if (props.processDefinitionId && props.processDefinitionId.includes('install_works_payment')) {
    // 安装工程合同付款流程
    canBackTaskKeyList = iwpCanBackTaskKeyList;
  } else if (form.value.flowApvContractPayInfo?.type === '1') {
    // 根据type字段判断：采购
    canBackTaskKeyList = mpCanBackTaskKeyList;
  } else if (form.value.flowApvContractPayInfo?.type === '2') {
    // 根据type字段判断：安装
    canBackTaskKeyList = iwpCanBackTaskKeyList;
  } else {
    // 默认使用采购流程的节点列表
    canBackTaskKeyList = mpCanBackTaskKeyList;
  }

  // 根据当前节点确定可驳回的节点
  // ... 其他逻辑
});
```

### 4. 优化驳回节点下拉框长度
**修改内容：**
```javascript
{
  label: "驳回节点",
  prop: "backTaskKey",
  type: "select",
  display: false,
  dicData: computeTaskKeyList,
  span: 6,  // 调整为6列，更短
  // ... 其他配置
}
```

## 流程类型区别

### 采购流程 (type=1, mp_payment)
**审批节点：**
1. 合同责任人 (`contract_resp`)
2. 供应链中心 (`supply_chain_center`)
3. 分管高管 (`senior`)
4. 财务经理 (`fpd`)

### 安装流程 (type=2, install_works_payment)
**审批节点：**
1. 安装合同责任人 (`install_contract_holder`)
2. 实施服务中心主任 (`isc_director`)
3. 常务副总 (`ev`)
4. 总经理 (`gm`)
5. 财务经理 (`fpd`)

## 验证步骤

### 1. 测试驳回页面提交
1. 进入付款审批驳回页面
2. 填写表单信息和审批意见
3. 点击提交按钮
4. 检查是否能正常提交并跳转

### 2. 测试已办理任务查看
1. 进入已办理任务列表
2. 点击查看详情
3. 确认不显示审批意见和提交按钮
4. 只显示关闭按钮

### 3. 测试流程类型区分
1. 创建采购类型的付款审批单
2. 进入审批页面，检查可驳回节点列表
3. 创建安装类型的付款审批单
4. 进入审批页面，检查可驳回节点列表是否不同

## 注意事项

1. **流程定义Key提取**：确保从 `processDefinitionId` 正确提取流程定义Key
2. **任务状态判断**：通过 `taskId` 是否存在来判断是否为待办理任务
3. **流程类型判断**：优先使用 `processDefinitionId`，其次使用 `type` 字段
4. **向后兼容**：保持原有功能不受影响，只是增加了动态控制逻辑

## 总结

通过以上优化，解决了付款审批流程中的三个关键问题：
1. 修复了驳回页面提交失败的问题
2. 实现了根据任务状态动态显示审批区域
3. 区分了采购和安装流程的可驳回节点列表
4. 优化了用户界面的布局和交互体验

这些优化确保了付款审批流程的正常运行，同时提升了用户体验。
