# 驳回页面提交问题修复方案

## 问题描述
用户反馈：驳回页面（`flowApvContractPayInfoReEdit.vue`）点击提交按钮没有反应。

## 问题分析

### 1. 原始问题
通过代码分析，发现以下几个潜在问题：

#### A. ref引用问题
- 代码中使用 `proxy.$refs.payInfoFormRef` 但这是Vue 2的写法
- Vue 3中应该使用 `payInfoFormRef.value` 的方式
- 缺少 `payInfoFormRef` 的定义

#### B. 流程定义Key缺失
- 后端需要通过 `processDefinitionKey` 来判断是采购流程还是安装流程
- 原代码中缺少这个关键字段

#### C. 错误处理不完善
- 缺少详细的错误日志和调试信息
- 异常处理不够完善

## 修复方案

### 1. 修复ref引用问题

**问题代码：**
```javascript
// 缺少ref定义
const auditFormRef = ref(null);
// 缺少payInfoFormRef定义

// 错误的引用方式
let formValidRes = await proxy.$refs.payInfoFormRef.validForm();
```

**修复后：**
```javascript
// 添加ref定义
const auditFormRef = ref(null);
const payInfoFormRef = ref(null); // 添加表单ref

// 正确的引用方式
let formValidRes = await payInfoFormRef.value.validForm();
```

### 2. 添加流程定义Key

**修复代码：**
```javascript
// 获取流程定义Key - 从processDefinitionId中提取
let processDefinitionKey = '';
if (props.processDefinitionId) {
    const parts = props.processDefinitionId.split(':');
    if (parts.length >= 1) {
        processDefinitionKey = parts[0];
    }
}

let params = Object.assign({}, payInfoData, {
  flow: {
    // ... 其他字段
    processDefinitionKey: processDefinitionKey, // 添加流程定义Key
    // ... 其他字段
  }
});
```

### 3. 增强错误处理和调试

**添加的调试功能：**
```javascript
// 1. 详细的控制台日志
console.log('=== 开始提交驳回表单 ===');
console.log('点击事件已触发');

// 2. 用户友好的提示
proxy.$Message.info('提交按钮已点击，开始处理...');

// 3. ref存在性检查
if (!payInfoFormRef.value) {
  console.error('payInfoFormRef 不存在');
  proxy.$Message.warning("表单未加载完成，请稍后再试");
  return false;
}

// 4. 详细的错误信息
catch (error) {
  console.error('重新编辑提交失败:', error);
  console.error('错误详情:', error.response || error.message);
  
  let errorMsg = "提交异常，请联系管理员";
  if (error.response && error.response.data && error.response.data.msg) {
    errorMsg = error.response.data.msg;
  }
  
  proxy.$Message({message: errorMsg, type: "error"});
}
```

### 4. 添加调试按钮

为了方便排查问题，添加了一个调试按钮：

```vue
<el-button type="warning" @click="debugInfo" style="margin-left: 10px;">调试信息</el-button>
```

```javascript
function debugInfo() {
  console.log('=== 调试信息 ===');
  console.log('props:', props);
  console.log('form.value:', form.value);
  console.log('auditForm.value:', auditForm.value);
  console.log('payInfoFormRef.value:', payInfoFormRef.value);
  console.log('auditFormRef.value:', auditFormRef.value);
  
  // 测试表单校验
  if (payInfoFormRef.value) {
    payInfoFormRef.value.validForm().then(res => {
      console.log('表单校验结果:', res);
    }).catch(err => {
      console.error('表单校验错误:', err);
    });
  }
}
```

## 测试步骤

### 1. 基础功能测试
1. 进入驳回页面
2. 点击"调试信息"按钮，查看控制台输出
3. 确认所有ref都正确加载
4. 确认表单数据正确

### 2. 提交功能测试
1. 填写审批意见
2. 点击"提交"按钮
3. 查看控制台日志，确认：
   - 点击事件被触发
   - 表单校验通过
   - 参数构建正确
   - 网络请求发送

### 3. 错误处理测试
1. 故意留空审批意见，测试校验
2. 测试网络异常情况
3. 确认错误信息正确显示

## 关键修改文件

**文件：** `src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoReEdit.vue`

**主要修改：**
1. 添加 `payInfoFormRef` ref定义
2. 修复Vue 3的ref引用方式
3. 添加 `processDefinitionKey` 字段
4. 增强错误处理和日志
5. 添加调试功能

## 预期结果

修复后，驳回页面应该能够：
1. 正确响应提交按钮点击
2. 正确校验表单和审批意见
3. 成功提交到后端
4. 显示正确的成功或错误信息
5. 提供详细的调试信息

## 注意事项

1. **调试按钮**：修复完成后可以移除调试按钮
2. **控制台日志**：生产环境可以减少日志输出
3. **向后兼容**：确保修改不影响其他功能
4. **测试覆盖**：建议测试不同的流程类型（采购/安装）

## 后续优化建议

1. 统一所有审批页面的错误处理方式
2. 添加更完善的表单校验提示
3. 考虑添加提交前的确认对话框
4. 优化用户体验，如加载状态提示等
