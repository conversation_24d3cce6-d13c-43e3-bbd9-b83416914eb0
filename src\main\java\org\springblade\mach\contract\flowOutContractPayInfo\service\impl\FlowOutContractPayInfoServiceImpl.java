/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowOutContractPayInfo.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.aspose.words.LoadFormat;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.google.common.collect.Maps;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.constant.MachConst;
import org.springblade.common.utils.*;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ResourceUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.mach.contract.flowElectricalCabinetContractDetails.service.IFlowElectricalCabinetContractDetailsService;
import org.springblade.mach.contract.flowElectricalCabinetContractPayment.pojo.entity.FlowElectricalCabinetContractPaymentEntity;
import org.springblade.mach.contract.flowElectricalCabinetContractPayment.service.IFlowElectricalCabinetContractPaymentService;
import org.springblade.mach.contract.flowEntrustContractDetails.service.IFlowEntrustContractDetailsService;
import org.springblade.mach.contract.flowEntrustDetailsPayment.pojo.entity.FlowEntrustDetailsPaymentEntity;
import org.springblade.mach.contract.flowEntrustDetailsPayment.service.IFlowEntrustDetailsPaymentService;
import org.springblade.mach.contract.flowOutContractPayDetail.pojo.entity.FlowOutContractPayDetailEntity;
import org.springblade.mach.contract.flowOutContractPayDetail.service.IFlowOutContractPayDetailService;
import org.springblade.mach.contract.flowOutContractPayInfo.excel.FlowOutContractPayInfoExcel;
import org.springblade.mach.contract.flowOutContractPayInfo.mapper.FlowOutContractPayInfoMapper;
import org.springblade.mach.contract.flowOutContractPayInfo.pojo.entity.FlowOutContractPayInfoEntity;
import org.springblade.mach.contract.flowOutContractPayInfo.pojo.vo.FlowOutContractPayInfoVO;
import org.springblade.mach.contract.flowOutContractPayInfo.service.IFlowOutContractPayInfoService;
import org.springblade.mach.contract.flowOutContractPayInfo.wrapper.FlowOutContractPayInfoWrapper;
import org.springblade.mach.contract.flowOutContractPayPaymentReal.pojo.entity.FlowOutContractPayPaymentRealEntity;
import org.springblade.mach.contract.flowOutContractPayPaymentReal.pojo.vo.FlowOutContractPayPaymentRealVO;
import org.springblade.mach.contract.flowOutContractPayPaymentReal.service.IFlowOutContractPayPaymentRealService;
import org.springblade.mach.contract.flowProcessingContractDetails.service.IFlowProcessingContractDetailsService;
import org.springblade.mach.contract.flowProcessingContractPayment.pojo.entity.FlowProcessingContractPaymentEntity;
import org.springblade.mach.contract.flowProcessingContractPayment.service.IFlowProcessingContractPaymentService;
import org.springblade.mach.pm.flowProjectPay.pojo.entity.FlowProjectPayEntity;
import org.springblade.mach.pm.projectEqDetails.pojo.entity.FlowProjectEqDetailsEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 外协合同付款单 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Service
public class FlowOutContractPayInfoServiceImpl extends BaseServiceImpl<FlowOutContractPayInfoMapper, FlowOutContractPayInfoEntity> implements IFlowOutContractPayInfoService {

	@Autowired
	private IFlowService flowService;

	@Autowired
	private IFlowOutContractPayPaymentRealService flowOutContractPayPaymentRealService;

	@Autowired
	private IFlowOutContractPayDetailService flowOutContractPayDetailService;

	@Autowired
	private IFlowEntrustDetailsPaymentService flowEntrustDetailsPaymentService;
	@Autowired
	private IFlowElectricalCabinetContractPaymentService flowElectricalCabinetContractPaymentService;
	@Autowired
	private IFlowProcessingContractPaymentService flowProcessingContractPaymentService;

	@Lazy
	@Autowired
	private IFlowEntrustContractDetailsService flowEntrustContractDetailsService;
	@Lazy
	@Autowired
	private IFlowElectricalCabinetContractDetailsService flowElectricalCabinetContractDetailsService;
	@Lazy
	@Autowired
	private IFlowProcessingContractDetailsService flowProcessingContractDetailsService;


	@Override
	public IPage<FlowOutContractPayInfoVO> selectFlowOutContractPayInfoPage(IPage<FlowOutContractPayInfoVO> page, FlowOutContractPayInfoVO flowOutContractPayInfo) {
		do {
			if(MachUtils.hasRole("payInfoViewAll") || MachUtils.isAdmin()){//付款单查看全部
				break;
			}
			flowOutContractPayInfo.setCreateUser(AuthUtil.getUserId());
		}while (false);
		return page.setRecords(baseMapper.selectFlowOutContractPayInfoPage(page, flowOutContractPayInfo));
	}


	@Override
	public List<FlowOutContractPayInfoExcel> exportFlowOutContractPayInfo(Wrapper<FlowOutContractPayInfoEntity> queryWrapper) {
		List<FlowOutContractPayInfoExcel> flowOutContractPayInfoList = baseMapper.exportFlowOutContractPayInfo(queryWrapper);
		//flowOutContractPayInfoList.forEach(flowOutContractPayInfo -> {
		//	flowOutContractPayInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, FlowOutContractPayInfo.getType()));
		//});
		return flowOutContractPayInfoList;
	}

    @Override
    public Integer getMaxCode(String partyA) {
        return baseMapper.getMaxCode(partyA);
    }

	@Override
	@Transactional(readOnly = false)
	public R submit(FlowOutContractPayInfoEntity flowOutContractPayInfo) {
		if(!StrUtil.isAllNotBlank(flowOutContractPayInfo.getPartyA(),flowOutContractPayInfo.getPartyB())){
			return R.fail("创建失败，甲方、乙方不能为空");
		}

		Long count = this.lambdaQuery()
			.eq(FlowOutContractPayInfoEntity::getCode, flowOutContractPayInfo.getCode())
			.count();
		if(count > 0){
			return R.fail("创建失败，编号重复请重新打开页面");
		}

		// 总金额
		BigDecimal amount = BigDecimal.ZERO;
		for (FlowOutContractPayPaymentRealVO p : flowOutContractPayInfo.getPayPaymentList()) {
			BigDecimal contractAmount = new BigDecimal(p.getEqPaymentAmount());
			amount = amount.add(contractAmount);
		}
		flowOutContractPayInfo.setAmount(amount);
		flowOutContractPayInfo.setStatus(Integer.valueOf(FlowOutContractPayInfoEntity.STATUS[0]));

		// 保存付款单
		super.saveOrUpdate(flowOutContractPayInfo);

		// 保存支付方式
		List<FlowOutContractPayDetailEntity> payDetailList = flowOutContractPayInfo.getPayDetailList();
		for (FlowOutContractPayDetailEntity flowOutContractPayDetail : payDetailList) {
			flowOutContractPayDetail.setPayInfoId(flowOutContractPayInfo.getId());
			flowOutContractPayDetailService.saveOrUpdate(flowOutContractPayDetail);
		}

		// 保存关联的请款信息
		List<FlowOutContractPayPaymentRealVO> paymentList = flowOutContractPayInfo.getPayPaymentList();
		for (FlowOutContractPayPaymentRealVO real : paymentList) {
			real.setPayInfoId(flowOutContractPayInfo.getId());
			real.setStatus(Integer.valueOf(FlowOutContractPayPaymentRealEntity.STATUS[1]));
			flowOutContractPayPaymentRealService.saveOrUpdate(real);
		}

		// 更新付款说明
		flowOutContractPayInfo.setPayDesc(this.getPayDesc(flowOutContractPayInfo));
		this.lambdaUpdate()
			.set(FlowOutContractPayInfoEntity::getPayDesc, flowOutContractPayInfo.getPayDesc())
			.eq(FlowOutContractPayInfoEntity::getId,flowOutContractPayInfo.getId())
			.update();

		// 启动付款申请流程
		this.audit(FlowOutContractPayInfoWrapper.build().entityVO(flowOutContractPayInfo));

//		// 构造付款说明
//		List<Long> paymentIds = new ArrayList<>();
//		StringBuilder payDesc = new StringBuilder();
//		for (FlowOutContractPayPaymentRealVO flowOutContractPayPaymentReal : flowOutContractPayInfo.getPayPaymentList()) {
//			String s1 = StrUtil.removeAll(flowOutContractPayPaymentReal.getCustomerName(), "有限公司");//xx公司
//			String s2 = DictBizCache.getValue("money_type",flowOutContractPayPaymentReal.getMoneyType());//付款类型
//			String s3 = flowOutContractPayPaymentReal.getEqPaymentProportion()+"%";//付款比例
//			String s4 = "";//付款至比例
//			if(!StrUtil.equalsAny(flowOutContractPayPaymentReal.getMoneyType(),FlowProjectPayEntity.MONEY_TYPE[0],FlowProjectPayEntity.MONEY_TYPE[1])){//非 全款/预付款
//				//查出付至xx%（已经付款过的）
//				Double allPayProportion = flowProcessingContractPaymentService.getAllPayProportion(flowOutContractPayPaymentReal.getEqCode());
//				s4 = " 付至"+ allPayProportion + "%";
//
//				// 当前付款比例和总付款比例一致，则不显示
//				if(StrUtil.equals(allPayProportion+"%",s3)){
//					s4 = "";
//				}
//			}
//			payDesc.append(StrUtil.format("{}{}{}{} ",s1,s2,s3,s4));
//			paymentIds.add(flowOutContractPayPaymentReal.getId());
//		}
//		flowOutContractPayInfo.setPayDesc(payDesc.toString());
//		// 保存付款单
//		super.saveOrUpdate(flowOutContractPayInfo);
//
//		// 更新请款单付款状态
//		if(!paymentIds.isEmpty()) {
//			flowProcessingContractPaymentService.lambdaUpdate()
//				.set(FlowProcessingContractPaymentEntity::getPaymentStatus, 1 == flowOutContractPayInfo.getStatus() ? "1" : "0")
//				.in(FlowProcessingContractPaymentEntity::getId, paymentIds)
//				.update();
//		}

		return R.success("保存成功");
	}

	/**
	 * @return String
	 * 获取付款说明
	 */
	@Override
	public String getPayDesc(FlowOutContractPayInfoEntity flowOutContractPayInfo) {
		StringBuilder payDesc = new StringBuilder();
		List<FlowOutContractPayPaymentRealVO> payPaymentList = flowOutContractPayInfo.getPayPaymentList();
		if(null != payPaymentList){
			// 先按合同编号分组，再按公司分组
			Map<String, Map<String, List<FlowOutContractPayPaymentRealVO>>> contractGroups = payPaymentList.stream()
				.collect(
					Collectors.groupingBy(FlowOutContractPayPaymentRealEntity::getContractCode, // 先按合同编号分组
					Collectors.groupingBy(payment -> StrUtil.removeAll(payment.getCustomerName(), "有限公司")))); // 再按公司分组

			// 处理每个合同的付款信息
			for (Map.Entry<String, Map<String, List<FlowOutContractPayPaymentRealVO>>> contractEntry : contractGroups.entrySet()) {
				String contractCode = contractEntry.getKey();
				Map<String, List<FlowOutContractPayPaymentRealVO>> companyGroups = contractEntry.getValue();

				// 处理每个公司的付款信息
				for (Map.Entry<String, List<FlowOutContractPayPaymentRealVO>> companyEntry : companyGroups.entrySet()) {
					String companyName = companyEntry.getKey();
					List<FlowOutContractPayPaymentRealVO> companyPayments = companyEntry.getValue();

					// 每个公司只生成一条付款说明
					String companyPayDesc = "";

					// 首先检查该公司的合同是否有已付款记录
					boolean hasPaymentHistory = false;
					double totalPaidProportion = 0d;
					FlowOutContractPayPaymentRealVO currentPayment;

					// 先找出该公司的合同详情ID，用于查询已付款记录
					if (!companyPayments.isEmpty()) {
						// 按申请时间排序，取最新的一条记录
						companyPayments.sort((a, b) -> {
							if (a.getPaymentAppDate() == null && b.getPaymentAppDate() == null) {
								return 0;
							} else if (a.getPaymentAppDate() == null) {
								return 1;
							} else if (b.getPaymentAppDate() == null) {
								return -1;
							}
							return b.getPaymentAppDate().compareTo(a.getPaymentAppDate());
						});

						// 使用最新的付款记录
						FlowOutContractPayPaymentRealVO firstPayment = companyPayments.get(0);
						Long contractDetailId = firstPayment.getContractDetailId();
						String contractType = firstPayment.getContractType();

						// 根据合同类型查询所有已付款记录
						if (StrUtil.equalsAny(contractType, FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[0], FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[1])) {
							// 大包/设计合同
							List<FlowEntrustDetailsPaymentEntity> list = flowEntrustDetailsPaymentService.lambdaQuery()
								.eq(FlowEntrustDetailsPaymentEntity::getType, FlowEntrustDetailsPaymentEntity.TYPES[0])
								.eq(FlowEntrustDetailsPaymentEntity::getPayStatus, MachConst.YES.getCode())
								.eq(FlowEntrustDetailsPaymentEntity::getContractDetailsId, contractDetailId)
								.list();

							if (!list.isEmpty()) {
								hasPaymentHistory = true;
								totalPaidProportion = list.stream()
									.mapToDouble(item -> Double.parseDouble(item.getProportion()))
									.sum();
							}
						} else if (contractType.equals(FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[3])) {
							// 电器柜合同
							List<FlowElectricalCabinetContractPaymentEntity> list = flowElectricalCabinetContractPaymentService.lambdaQuery()
								.eq(FlowElectricalCabinetContractPaymentEntity::getType, FlowElectricalCabinetContractPaymentEntity.TYPES[0])
								.eq(FlowElectricalCabinetContractPaymentEntity::getPayStatus, MachConst.YES.getCode())
								.eq(FlowElectricalCabinetContractPaymentEntity::getContractDetailsId, contractDetailId)
								.list();

							if (!list.isEmpty()) {
								hasPaymentHistory = true;
								totalPaidProportion = list.stream()
									.mapToDouble(item -> Double.parseDouble(item.getProportion()))
									.sum();
							}
						} else if (contractType.equals(FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[2])) {
							// 加工合同
							List<FlowProcessingContractPaymentEntity> list = flowProcessingContractPaymentService.lambdaQuery()
								.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[1])
								.eq(FlowProcessingContractPaymentEntity::getPayStatus, MachConst.YES.getCode())
								.eq(FlowProcessingContractPaymentEntity::getContractDetailsId, contractDetailId)
								.list();

							if (!list.isEmpty()) {
								hasPaymentHistory = true;
								totalPaidProportion = list.stream()
									.mapToDouble(item -> Double.parseDouble(item.getProportion()))
									.sum();
							}
						}

						// 计算当前分组中所有请款详情的付款比例总和
						double currentGroupTotalProportion = companyPayments.stream()
							.mapToDouble(item -> Double.parseDouble(item.getEqPaymentProportion()))
							.sum();

						// 选择当前付款（最新的一条记录）
						currentPayment = firstPayment;

						// 生成付款说明
						String paymentType = DictBizCache.getValue("money_type", currentPayment.getMoneyType()); // 付款类型
						String proportion = currentPayment.getEqPaymentProportion() + "%"; // 付款比例
						String paidToText = ""; // 付至比例文本
						// 如果有已付款记录，显示付至总比例（已付款比例 + 当前组内请款比例总和）
						if (!StrUtil.equalsAny(currentPayment.getMoneyType(),FlowProjectPayEntity.MONEY_TYPE[0],FlowProjectPayEntity.MONEY_TYPE[1])) {
							double totalPaidWithCurrent = totalPaidProportion + currentGroupTotalProportion;
							paidToText = " 付至" + totalPaidWithCurrent + "%";
						}

						// 格式化输出
						companyPayDesc = StrUtil.format("{}{}{}{} ", companyName, paymentType, proportion, paidToText);
						payDesc.append(companyPayDesc);
					}
				}
			}
		}
		return payDesc.toString();
	}

	@Override
	public void outputPayInfoPdf(Long id, HttpServletRequest request, HttpServletResponse response) {
		// poi-tl模版
		String poiTl = "classpath:/poi-tl/out_contract_payinfo.docx";//外部合同付款单

		// 付款单信息
		FlowOutContractPayInfoEntity flowOutContractPayInfo = this.getDetail(id);

		// 查出关联的请款列表
		List<FlowOutContractPayPaymentRealVO> records = flowOutContractPayPaymentRealService.selectByPayInfoId(id);
		flowOutContractPayInfo.setPayPaymentList(records);

		// 统一转成vo
		FlowOutContractPayInfoVO flowOutContractPayInfoVO = FlowOutContractPayInfoWrapper.build().entityVO(flowOutContractPayInfo);

		// 处理字典信息
		flowOutContractPayInfoVO.setBankChange("1".equals(flowOutContractPayInfoVO.getBankChange())?"是":"否");
		flowOutContractPayInfoVO.setInvoiceStatus("1".equals(flowOutContractPayInfoVO.getInvoiceStatus())?"已开":"否");
		flowOutContractPayInfoVO.setInstoreStatus("1".equals(flowOutContractPayInfoVO.getInstoreStatus())?"是":"否");

		List<FlowOutContractPayDetailEntity> payDetailList = flowOutContractPayInfoVO.getPayDetailList();
		for (FlowOutContractPayDetailEntity flowOutContractPayDetailEntity : payDetailList) {
			flowOutContractPayDetailEntity.setType(DictBizCache.getValue("out_contract_pay_type",flowOutContractPayDetailEntity.getType()));
		}
		flowOutContractPayInfoVO.setPayDetailList(payDetailList);

		//  先生成合同word，再生成PDF
		try{
			// 获取 Word 模板所在路径
			Resource resource = ResourceUtil.getResource(poiTl);
			LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
			LoopRowTableRenderPolicy policy1 = new LoopRowTableRenderPolicy();
			// 配置
			Configure configure = Configure.builder()
				//.bind("formatMoney", new FormatMoneyRenderPolicy())
				.bind("payDetailList",policy)
				.bind("payPaymentList",policy1)
				.useSpringEL()
				.build();

			BigDecimal amount = flowOutContractPayInfoVO.getAmount();
			flowOutContractPayInfoVO.setTotalAmountInWords(ConvertUpMoney.toChinese(amount));

			Map<String,Object> data = new HashMap<>();
			data.put("payDetailList", flowOutContractPayInfoVO.getPayDetailList());
			data.put("payPaymentList", flowOutContractPayInfoVO.getPayPaymentList());
			data.put("payinfo",flowOutContractPayInfoVO);
			data.put("applicationDate", DateUtil.formatDate(flowOutContractPayInfoVO.getCreateTime()));
			data.put("factoryName",DictBizCache.getValue("factory_name", flowOutContractPayInfoVO.getPartyA()));

			// 通过 XWPFTemplate 编译文件并渲染数据到模板中
			XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(),configure).render(data);

			// 生成到word字符流
			ByteArrayOutputStream fos = new ByteArrayOutputStream();
			template.writeAndClose(fos);
			ByteArrayInputStream inputStream = new ByteArrayInputStream(fos.toByteArray());
			fos = null;

			// 生成PDF 输出到页面
			String fileName = StrUtil.format("【{}】付款单.pdf",flowOutContractPayInfoVO.getCode());
			ServletUtil.setDownloadHeader(request,response,fileName);
			ServletOutputStream outputStream = response.getOutputStream();
			DocUtil.getInstance().createPdfWithInputStream(inputStream, LoadFormat.DOCX, outputStream);
            /*if(null != outputStream){
                try {
                    outputStream.close();
                }finally {
                    outputStream = null;
                }
            }*/
		}catch (Exception e){
			LogUtils.error(getClass(),"生成付款单异常:",e);
		}
	}

	/**
	 * 查看付款单关联合同pdf流
	 * @param id	付款申请ID
	 */
    @Override
	@Transactional(readOnly = false)
	public void outputPayInfoContractPdf(Long id, HttpServletRequest request, HttpServletResponse response) {
		try{
			// 付款单信息
			FlowOutContractPayInfoEntity flowOutContractPayInfo = this.getDetail(id);

			// 查出关联的请款列表
			List<FlowOutContractPayPaymentRealVO> records = flowOutContractPayPaymentRealService.selectByPayInfoId(id);
			//flowOutContractPayInfo.setPayPaymentList(records);

			// 取出所有合同id，去重
			Map<String,FlowOutContractPayPaymentRealVO> map = new LinkedHashMap<>();
			for (FlowOutContractPayPaymentRealVO real : records){
				String key = StrUtil.format("{}_{}",real.getContractType(),real.getContractDetailId());
				if(map.containsKey(key)){
					continue;
				}
				map.put(key, real);
			}
			// 循环拼接pdf
			List<byte[]> inputStreamList = new ArrayList<>();
			Iterator<String> it = map.keySet().iterator();
			while (it.hasNext()){
				String key = it.next();
				FlowOutContractPayPaymentRealVO real = map.get(key);
				String contractType = real.getContractType();
				Long contractDetailId = real.getContractDetailId();
				// 委托合同
				if (FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[0].equals(contractType) || FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[1].equals(contractType)){
					Kv kv = flowEntrustContractDetailsService.getContractPdf(contractDetailId);
					if(null == kv){
						continue;
					}
					byte[] bytes = (byte[]) kv.get("bytes");
					inputStreamList.add(bytes);
				}
				// 电气柜合同
				else if(FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[3].equals(contractType)){
					Kv kv = flowElectricalCabinetContractDetailsService.getContractPdf(contractDetailId);
					if(null == kv){
						continue;
					}
					byte[] bytes = (byte[]) kv.get("bytes");
					inputStreamList.add(bytes);
				}
				// 加工合同
				else if(FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[2].equals(contractType)){
					Kv kv = flowProcessingContractDetailsService.getContractPdf(contractDetailId);
					if(null == kv){
						continue;
					}
					byte[] bytes = (byte[]) kv.get("bytes");
					inputStreamList.add(bytes);
				}
			}

			String contractName = "付款申请【"+flowOutContractPayInfo.getCode()+"】关联合同";
			String fileName = contractName+".pdf";
			ServletUtil.setDownloadHeader(request,response,fileName);

			// 单个合同直接渲染
			if(inputStreamList.size() == 1){
				byte[] pdfBytes = inputStreamList.get(0);
				ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);
				ServletUtil.write(response, inputStream, "application/octet-stream");
			}
			// 多个合同合并成一个pdf
			else if(inputStreamList.size() > 1){
				ServletOutputStream outputStream = response.getOutputStream();
				InputStream[] isAry = new InputStream[inputStreamList.size()];
				for (int i = 0; i < inputStreamList.size(); i++) {
					byte[] pdfBytes = inputStreamList.get(i);
					isAry[i] = new ByteArrayInputStream(pdfBytes);
				}
				PdfUtil.getInstance().concatPdf(isAry,outputStream);
			}
		}catch (Exception e){
			log.error("查看付款申请关联合同异常:",e);
		}
    }

    @Override
	public FlowOutContractPayInfoEntity getDetail(Long id) {
		// 付款单详情
		FlowOutContractPayInfoEntity entity = super.getById(id);

		// 查出支付方式
		FlowOutContractPayDetailEntity where = new FlowOutContractPayDetailEntity();
		where.setPayInfoId(id);
		List<FlowOutContractPayDetailEntity> list = flowOutContractPayDetailService.list(Wrappers.query(where));
		entity.setPayDetailList(list);

		return entity;
	}

	/**
	 * 获取流程处理表单
	 * {@link org.springblade.flow.business.service.impl.FlowServiceImpl#getAuditFormVue 动态调用本方法}
	 * @param flow
	 * @return
	 */
	@Override
	public String auditForm(BladeFlow flow){
		String view = "flowContract/flowOutContractPayInfo/flowOutContractPayInfoDetails";

		if(StringUtil.isBlank(flow.getBusinessId())){
			return view;
		}

		if(flow.isFinishTask()){
			return view;
		}

		// 环节编号
		String taskDefKey = flow.getTaskDefinitionKey();
		if(StringUtil.isBlank(taskDefKey)){
			return view;
		}

		String processDefinitionId = flow.getProcessDefinitionId();
		ProcessEngine processEngine = flowService.getProcessEngine();
		// 获取流程定义Key
		RepositoryService repositoryService = processEngine.getRepositoryService();
		ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
			.processDefinitionId(processDefinitionId)
			.singleResult();
		String processDefinitionKey = processDefinition.getKey(); // 获取流程定义Key

		// 委内-合同-付款申请
		if(StrUtil.startWith(processDefinitionKey,FlowUtil.PD_CONTRACT_PAYINFO_IN_AUDIT[0])) {
			switch (taskDefKey){
				case "apply_edit"://发起人修改付款单
					view = "flowContract/flowOutContractPayInfo/flowOutContractPayInfoReEdit";
					break;
				case "financeManagerAudit"://财务经理审核
				case "managerAudit"://总经理审批
				case "tellerPayment"://出纳付款
					view = "flowContract/flowOutContractPayInfo/flowOutContractPayInfoAudit";
					break;
			}
		}
		// 委外-合同-付款申请
		else{
			switch (taskDefKey){
				case "apply_edit"://发起人修改付款单
					view = "flowContract/flowOutContractPayInfo/flowOutContractPayInfoReEdit";
					break;
				case "manager_audit"://部门领导审批
				case "financeAudit"://财务审批
				case "financeManagerAudit"://财务经理审批
				case "dgmAudit"://常务副总审批
					view = "flowContract/flowOutContractPayInfo/flowOutContractPayInfoAudit";
					break;
				case "payerAudit"://付款人审批 确认支付方式
					view = "flowContract/flowOutContractPayInfo/flowOutContractPayInfoFinanceAudit";
					break;
				case "managerAudit"://总经理审批
				case "tellerPayment"://出纳付款
					view = "flowContract/flowOutContractPayInfo/flowOutContractPayInfoAudit";
					break;
			}
		}

		return view;
	}

	/**
	 * 获取表单数据详情
	 * @param flow
	 * @return
	 */
	@Override
	public R auditFormDetail(BladeFlow flow){
		if(StringUtil.isBlank(flow.getBusinessId())){
			return R.fail("businessId为空");
		}
		Kv model = Kv.init();

		// 付款信息
		FlowOutContractPayInfoEntity flowOutContractPayInfo = this.getById(Long.valueOf(flow.getBusinessId()));

		// 获取流程定义Key
		String procInsId = flowOutContractPayInfo.getProcInsId();
		ProcessEngine processEngine = flowService.getProcessEngine();
		HistoricProcessInstance historicProcessInstance = processEngine.getHistoryService().createHistoricProcessInstanceQuery().processInstanceId(procInsId).singleResult();
		String processDefinitionKey = historicProcessInstance.getProcessDefinitionKey();
		flow.setProcessDefinitionKey(processDefinitionKey);

//		// 委内-合同-付款申请
//		if(StrUtil.startWith(processDefinitionKey,FlowUtil.PD_CONTRACT_PAYINFO_IN_AUDIT[0])) {
//			model.set("processDefinitionKey",FlowUtil.PD_CONTRACT_PAYINFO_IN_AUDIT[0]);
//		}
//		// 委外-合同-付款申请
//		else{
//			model.set("processDefinitionKey",FlowUtil.PD_CONTRACT_PAYINFO_IN_AUDIT[1]);
//		}


		List<FlowOutContractPayDetailEntity> list1 = flowOutContractPayDetailService.lambdaQuery()
			.eq(FlowOutContractPayDetailEntity::getPayInfoId, flowOutContractPayInfo.getId())
			.list();
		flowOutContractPayInfo.setPayDetailList(list1);

		// 请款信息
		List<FlowOutContractPayPaymentRealVO> list2 = flowOutContractPayPaymentRealService.selectByPayInfoId(flowOutContractPayInfo.getId());
		flowOutContractPayInfo.setPayPaymentList(list2);

		flowOutContractPayInfo.setFlow(flow);
		model.set("flowOutContractPayInfo",flowOutContractPayInfo);

		// 已经过了自己参与的环节
		if (flow.isFinishTask()) {
			return R.data(model);
		}

		model.set("audit", true);
		return R.data(model);
	}

	/**
	 * 流程提交
	 * @param flowOutContractPayInfo
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public R audit(FlowOutContractPayInfoVO flowOutContractPayInfo){
		Map<String, Object> vars = Maps.newHashMap();
		//启动流程
		if (StringUtils.isBlank(flowOutContractPayInfo.getProcInsId())) {
			vars.put("applyUser", AuthUtil.getUserAccount());//记录发起人
			vars.put("title",flowOutContractPayInfo.getCode());

			// 委内-合同-付款
			if(StrUtil.equalsAny(flowOutContractPayInfo.getPartyB(),FlowProjectEqDetailsEntity.FACTORY_TYPE[0],FlowProjectEqDetailsEntity.FACTORY_TYPE[1],FlowProjectEqDetailsEntity.FACTORY_TYPE[5])) {
				vars.put("auditUser", DictBizCache.getKey("office_manager", "北京财务经理"));
				flowService.startProcessInstanceByKey(FlowUtil.PD_CONTRACT_PAYINFO_IN_AUDIT[0], FlowUtil.PD_CONTRACT_PAYINFO_IN_AUDIT[1], String.valueOf(flowOutContractPayInfo.getId()), vars);
			}
			// 委外-合同-付款
			else{
				String partyA = flowOutContractPayInfo.getPartyA();//生产基地转为公司代号
				if (FlowProjectEqDetailsEntity.FACTORY_TYPE[0].equals(partyA)) {//天津
					vars.put("managerUser", MachUtils.getManagerByRole("供应链中心副经理"));
				} else {//江苏
					vars.put("managerUser", MachUtils.getManagerByRole("江苏工厂副总经理"));
				}
				flowService.startProcessInstanceByKey(FlowUtil.PD_CONTRACT_PAYINFO_OUT_AUDIT[0], FlowUtil.PD_CONTRACT_PAYINFO_OUT_AUDIT[1], String.valueOf(flowOutContractPayInfo.getId()), vars);
			}
			//记录申请人
			this.lambdaUpdate()
				.set(FlowOutContractPayInfoEntity::getApplicantName, AuthUtil.getNickName())
				.eq(FlowOutContractPayInfoEntity::getId, flowOutContractPayInfo.getId())
				.update();
			LogUtils.info(getClass(), org.springblade.core.tool.utils.DateUtil.format(new Date(), "yy--MM-dd HH:mm:ss") + "启动付款申请[" + flowOutContractPayInfo.getCode() + "]的流程");
		} else {
			String flag = flowOutContractPayInfo.getFlow().getFlag();
			String taskDefKey = flowOutContractPayInfo.getFlow().getTaskDefinitionKey();
			String processDefinitionIdStr = flowOutContractPayInfo.getFlow().getProcessDefinitionId();

			// 是否保存审批人
			boolean saveAuditUserFlag = true;

			// 委内-合同付款-节点处理
			if(StrUtil.startWith(processDefinitionIdStr,FlowUtil.PD_CONTRACT_PAYINFO_IN_AUDIT[0])) {

				String businessId = flowOutContractPayInfo.getFlow().getBusinessId();
				FlowOutContractPayInfoEntity flowOutContractPayInfoEntity = this.getById(Long.valueOf(businessId));

				// 生产基地转换为所属公司
				String companyA = MachUtils.getOfficeByCompany(flowOutContractPayInfoEntity.getPartyA());

				switch (taskDefKey) {
					// 发起人修改
					case "apply_edit":
						this.reEditPayInfo(flowOutContractPayInfo);
						break;
					// 财务经理审核付款单
					case "financeManagerAudit":
						vars.put("pass", "yes".equals(flag)?1:0);
						break;
					// 总经理审核
					case "managerAudit":
						if ("yes".equals(flag)) {
							vars.put("pass", 1);
							vars.put("paymentUser", DictBizCache.getKey("office_manager", companyA+"出纳"));
						}else{
							vars.put("pass", 0);
						}
						break;
					// 出纳付款
					case "tellerPayment":
						// 记录付款人，不记录审批人
						saveAuditUserFlag = false;
						flowOutContractPayInfo.setPayerName(AuthUtil.getNickName());
						// 设置审批完成
						flowOutContractPayInfo.setStatus(Integer.valueOf(FlowOutContractPayInfoEntity.STATUS[1]));
						flowOutContractPayInfo.setPayTime(DateUtil.date());
						this.updateById(flowOutContractPayInfo);
						this.updateContractPayEnd(flowOutContractPayInfo);
						break;
					default:
						break;
				}

			}
			// 委外-合同付款-节点处理
			else{
				switch (taskDefKey) {
					case "apply_edit"://发起人修改付款单
						{
							this.reEditPayInfo(flowOutContractPayInfo);
							String partyA = flowOutContractPayInfo.getPartyA();//生产基地转为公司代号
							if (FlowProjectEqDetailsEntity.FACTORY_TYPE[0].equals(partyA)) {//天津
								vars.put("managerUser", MachUtils.getManagerByRole("供应链中心副经理"));
							} else {//江苏
								vars.put("managerUser", MachUtils.getManagerByRole("江苏工厂副总经理"));
							}
						}
						break;
					case "manager_audit"://部门领导审批
						if ("yes".equals(flag)) {
							vars.put("pass", 1);
							// 放入下一个节点的审批人
							vars.put("financeUser", DictBizCache.getKey("contract_signer", "加工合同付款财务审批"));
						} else {
							vars.put("pass", 0);
							vars.put("back", FlowOutContractPayInfoEntity.taskKeys.getInt("apply_edit"));//驳回到某个节点
							flowOutContractPayInfo.getFlow().setComment("[驳回]" + flowOutContractPayInfo.getFlow().getComment());
						}
						break;
					case "financeAudit"://财务审批
						if ("yes".equals(flag)) {
							vars.put("pass", 1);
							// 放入下一个节点的审批人
							vars.put("financeManagerUser", DictBizCache.getKey("contract_signer", "加工合同付款财务经理审批"));
						} else {
							vars.put("pass", 0);
							vars.put("back", FlowOutContractPayInfoEntity.taskKeys.getInt(flowOutContractPayInfo.getBackTaskKey()));//驳回到某个节点
							flowOutContractPayInfo.getFlow().setComment("[驳回]" + flowOutContractPayInfo.getFlow().getComment());
						}
						break;
					case "financeManagerAudit"://财务经理审批
						if ("yes".equals(flag)) {
							vars.put("pass", 1);
							// 放入下一个节点的审批人，同时放入出纳
							{
								String partyA = flowOutContractPayInfo.getPartyA();//生产基地转为公司代号
								if (FlowProjectEqDetailsEntity.FACTORY_TYPE[0].equals(partyA)) {//天津
									vars.put("dgmUser", MachUtils.getManagerByRole("天津工厂常务副总"));
									vars.put("tellerUser", MachUtils.getManagerByRole("天津出纳"));
								} else {//江苏
									vars.put("dgmUser", MachUtils.getManagerByRole("江苏工厂常务副总"));
									vars.put("tellerUser", MachUtils.getManagerByRole("江苏出纳"));
								}
							}
						} else {
							vars.put("pass", 0);
							vars.put("back", FlowOutContractPayInfoEntity.taskKeys.getInt(flowOutContractPayInfo.getBackTaskKey()));//驳回到某个节点
							flowOutContractPayInfo.getFlow().setComment("[驳回]" + flowOutContractPayInfo.getFlow().getComment());
						}
						break;
					case "dgmAudit"://常务副总审批
						if ("yes".equals(flag)) {
							// 根据金额情况判断是否需要总经理审批,超过或等于2万需要总经理审批
							if (flowOutContractPayInfo.getAmount().compareTo(new BigDecimal("20000")) >= 0) {
								vars.put("pass", 1);//需付款人及总经理审批
								// 放入下一个节点的审批人
								vars.put("payerUser", DictBizCache.getKey("contract_signer", "加工合同付款付款人审批"));
							}
							// 金额小于2万
							else {
								vars.put("pass", 2);//下一步-出纳签收
								//自动加入支付方式
								List<FlowOutContractPayDetailEntity> payDetailList = new ArrayList<>();
								FlowOutContractPayDetailEntity flowOutContractPayDetail = new FlowOutContractPayDetailEntity();
								flowOutContractPayDetail.setId(null);
								flowOutContractPayDetail.setPayInfoId(flowOutContractPayInfo.getId());
								flowOutContractPayDetail.setAmount(flowOutContractPayInfo.getAmount());
								flowOutContractPayDetail.setType(DictBizCache.getKey("out_contract_pay_type","电汇"));
								flowOutContractPayDetail.setTicketNo("无");
								payDetailList.add(flowOutContractPayDetail);
								flowOutContractPayInfo.setPayDetailList(payDetailList);
								// 保存支付方式
								this.updatePayDetail(flowOutContractPayInfo);
							}
						} else {
							vars.put("pass", 0);
							vars.put("back", FlowOutContractPayInfoEntity.taskKeys.getInt(flowOutContractPayInfo.getBackTaskKey()));//驳回到某个节点
							flowOutContractPayInfo.getFlow().setComment("[驳回]" + flowOutContractPayInfo.getFlow().getComment());
						}
						break;
					case "payerAudit"://付款人审批  - 同时添加支付方式
						// 保存付款单（支付说明）
						this.saveOrUpdate(flowOutContractPayInfo);
						// 保存支付方式
						this.updatePayDetail(flowOutContractPayInfo);

						if ("yes".equals(flag)) {
							vars.put("pass", 1);
						} else {
							vars.put("pass", 0);
							vars.put("back", FlowOutContractPayInfoEntity.taskKeys.getInt(flowOutContractPayInfo.getBackTaskKey()));//驳回到某个节点
							flowOutContractPayInfo.getFlow().setComment("[驳回]" + flowOutContractPayInfo.getFlow().getComment());
						}
						break;
					case "managerAudit"://总经理审批
						if ("yes".equals(flag)) {
							vars.put("pass", 1);
						} else {
							vars.put("pass", 0);
							vars.put("back", FlowOutContractPayInfoEntity.taskKeys.getInt(flowOutContractPayInfo.getBackTaskKey()));//驳回到某个节点
							flowOutContractPayInfo.getFlow().setComment("[驳回]" + flowOutContractPayInfo.getFlow().getComment());
						}
						break;
					case "tellerPayment"://出纳签收
						// 记录付款人，不记录审批人
						saveAuditUserFlag = false;
						flowOutContractPayInfo.setPayerName(AuthUtil.getNickName());
						// 设置审批完成
						flowOutContractPayInfo.setStatus(Integer.valueOf(FlowOutContractPayInfoEntity.STATUS[1]));
						flowOutContractPayInfo.setPayTime(DateUtil.date());
						this.updateById(flowOutContractPayInfo);
						this.updateContractPayEnd(flowOutContractPayInfo);
						break;
					default:
						break;
				}
			}
			// 完成任务
			flowService.completeTask(flowOutContractPayInfo.getFlow().getTaskId(), flowOutContractPayInfo.getFlow().getProcessInstanceId(), flowOutContractPayInfo.getFlow().getComment(), vars);

			// 记录审批人
			if(saveAuditUserFlag) {
				String approverNameStr = this.getById(flowOutContractPayInfo.getId()).getApproverName();
				List<String> approverNameList = Func.toStrList(approverNameStr);
				LinkedHashSet<String> approverNameSet = new LinkedHashSet<>(approverNameList);
				approverNameStr = Func.join(approverNameSet);
				this.lambdaUpdate()
					.set(FlowOutContractPayInfoEntity::getApproverName, approverNameStr)
					.eq(FlowOutContractPayInfoEntity::getId, flowOutContractPayInfo.getId())
					.update();
			}
		}
		return R.success("提交成功");
	}

	@Override
    public FlowOutContractPayInfoEntity findByProcInsId(String processInstanceId) {
        // 先从缓存拿
        FlowOutContractPayInfoEntity entity = null;
        try {
            entity = CacheUtil.get("flowOutContractPayInfoProcIns", "id:", processInstanceId, FlowOutContractPayInfoEntity.class, false);
        } catch (Exception e) {
            // 记录反序列化异常
            log.error("Failed to deserialize from cache for processInstanceId: {}");
        }
        if (entity != null) {
            return entity;
        }

        // 缓存找不到去数据库取
        FlowOutContractPayInfoEntity where = new FlowOutContractPayInfoEntity();
        where.setProcInsId(processInstanceId);
        FlowOutContractPayInfoEntity byProcInsId = baseMapper.selectOne(Condition.getQueryWrapper(where), false);
        if (byProcInsId != null) {
            try {
                CacheUtil.put("flowOutContractPayInfoProcIns", "id:", byProcInsId.getProcInsId(), byProcInsId, false);
            } catch (Exception e) {
                // 记录序列化异常
                log.error("Failed to serialize into cache for processInstanceId: {}");
            }
        }
        return byProcInsId;
    }

	/**
	 * 更新各类合同及请款单状态
	 * @param flowOutContractPayInfo
	 */
	private void updateContractPayEnd(FlowOutContractPayInfoVO flowOutContractPayInfo) {
		List<FlowOutContractPayPaymentRealEntity> list = flowOutContractPayPaymentRealService.lambdaQuery()
			.eq(FlowOutContractPayPaymentRealEntity::getPayInfoId, flowOutContractPayInfo.getId())
			.list();
		// 按合同类型分组
		Map<String, List<FlowOutContractPayPaymentRealEntity>> map = list.stream().collect(Collectors.groupingBy(FlowOutContractPayPaymentRealEntity::getContractType));
		for (String key : map.keySet()) {
			List<Long> paymentIds = map.get(key).stream().map(FlowOutContractPayPaymentRealEntity::getPaymentId).collect(Collectors.toList());//请款单的id

			//根据合同类型更新请款单的付款状态
			// 大包/设计
			if(StrUtil.equalsAny(key,FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[0],FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[1])){
				//更新合同类型的单据为已完成
				flowEntrustDetailsPaymentService.lambdaUpdate()
					.set(FlowEntrustDetailsPaymentEntity::getPayStatus, MachConst.YES.getCode())
					.set(FlowEntrustDetailsPaymentEntity::getPayDate, DateUtil.date())
					.set(FlowEntrustDetailsPaymentEntity::getPayInfoId, flowOutContractPayInfo.getId())
					.eq(FlowEntrustDetailsPaymentEntity::getType,FlowEntrustDetailsPaymentEntity.TYPES[0])
					.in(FlowEntrustDetailsPaymentEntity::getPaymentId,paymentIds)
					.update();
			}
			// 电气柜
			else if(key.equals(FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[3])) {
				//更新合同类型的单据为已完成
				flowElectricalCabinetContractPaymentService.lambdaUpdate()
					.set(FlowElectricalCabinetContractPaymentEntity::getPayStatus, MachConst.YES.getCode())
					.set(FlowElectricalCabinetContractPaymentEntity::getPayDate, DateUtil.date())
					.set(FlowElectricalCabinetContractPaymentEntity::getPayInfoId, flowOutContractPayInfo.getId())
					.eq(FlowElectricalCabinetContractPaymentEntity::getType,FlowElectricalCabinetContractPaymentEntity.TYPES[0])
					.in(FlowElectricalCabinetContractPaymentEntity::getPaymentId,paymentIds)
					.update();
			}
			// 加工
			else if(key.equals(FlowOutContractPayPaymentRealEntity.CONTRACT_TYPE[2])){
				//更新合同类型的单据为已完成
				flowProcessingContractPaymentService.lambdaUpdate()
					.set(FlowProcessingContractPaymentEntity::getPayStatus, MachConst.YES.getCode())
					.set(FlowProcessingContractPaymentEntity::getPayDate, DateUtil.date())
					.set(FlowProcessingContractPaymentEntity::getPayInfoId, flowOutContractPayInfo.getId())
					.eq(FlowProcessingContractPaymentEntity::getType,FlowProcessingContractPaymentEntity.TYPE[1])
					.in(FlowProcessingContractPaymentEntity::getPaymentId,paymentIds)
					.update();
			}
		}
	}

	/**
	 * 发起人重新修改付款单
	 * @param flowOutContractPayInfo
	 */
	private void reEditPayInfo(FlowOutContractPayInfoVO flowOutContractPayInfo) {

		// 保存付款单
		this.saveOrUpdate(flowOutContractPayInfo);

		// 保存支付方式
		this.updatePayDetail(flowOutContractPayInfo);

		// 清除旧请款信息(置空+该状态)
		flowOutContractPayPaymentRealService.lambdaUpdate()
			.set(FlowOutContractPayPaymentRealEntity::getPayInfoId, null)
			.set(FlowOutContractPayPaymentRealEntity::getStatus,Integer.valueOf(FlowOutContractPayPaymentRealEntity.STATUS[0]))
			.eq(FlowOutContractPayPaymentRealEntity::getPayInfoId, flowOutContractPayInfo.getId())
			.update();
		// 保存请款信息
		List<FlowOutContractPayPaymentRealVO> payPaymentList = flowOutContractPayInfo.getPayPaymentList();
		if(null != payPaymentList){
			for (FlowOutContractPayPaymentRealVO payPayment : payPaymentList) {
				payPayment.setPayInfoId(flowOutContractPayInfo.getId());
				payPayment.setStatus(Integer.valueOf(FlowOutContractPayPaymentRealEntity.STATUS[1]));
				flowOutContractPayPaymentRealService.saveOrUpdate(payPayment);
			}
		}

		// 更新付款说明
		flowOutContractPayInfo.setPayDesc(this.getPayDesc(flowOutContractPayInfo));
		this.lambdaUpdate()
			.set(FlowOutContractPayInfoEntity::getPayDesc, flowOutContractPayInfo.getPayDesc())
			.eq(FlowOutContractPayInfoEntity::getId,flowOutContractPayInfo.getId())
			.update();
	}

	/**
	 * 保存支付方式
	 */
	private void updatePayDetail(FlowOutContractPayInfoVO flowOutContractPayInfo){
		// 清除旧支付方式
		flowOutContractPayDetailService.lambdaUpdate()
			.eq(FlowOutContractPayDetailEntity::getPayInfoId, flowOutContractPayInfo.getId())
			.remove();
		// 保存支付方式
		List<FlowOutContractPayDetailEntity> payDetailList = flowOutContractPayInfo.getPayDetailList();
		if(null != payDetailList){
			for (FlowOutContractPayDetailEntity payDetail : payDetailList) {
				payDetail.setId(null);
				payDetail.setPayInfoId(flowOutContractPayInfo.getId());
				flowOutContractPayDetailService.saveOrUpdate(payDetail);
			}
		}
	}

}
