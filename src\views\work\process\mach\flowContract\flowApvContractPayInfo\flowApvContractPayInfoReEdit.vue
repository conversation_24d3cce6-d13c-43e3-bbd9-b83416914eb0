<template>
  <!--
    组件：付款审批-重新编辑页面
    路径：src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoReEdit.vue
  -->
  <basic-container>
    <el-tabs v-model="flowTabSelected">
      <el-tab-pane label="流程信息" name="flow">
        <el-row>
          <el-col :span="24">
            <histoicFlowVue :processInstanceId="processInstanceId" :taskId="taskId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="流程图" name="diagram">
        <el-row>
          <el-col :span="24">
            <flowDiagramVue :processInstanceId="processInstanceId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="表单信息" name="form">
        <el-row>
          <el-col :span="24">
            <mach-card title="付款审批单">
              <apvContractPayInfoForm :flowApvContractPayInfo="form.flowApvContractPayInfo" ref="payInfoFormRef" />
            </mach-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="PDF预览" name="pdf">
        <el-row>
          <el-col :span="24">
            <machPdf :processInstanceId="processInstanceId" />
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <el-divider content-position="left">审批意见</el-divider>

    <avue-form :option="auditOption" v-model="auditForm" ref="auditFormRef">
      <!-- 隐藏avue-form自带的操作按钮 -->
      <template #menu-form="{}">
        <!-- 不显示任何内容，隐藏清空和提交按钮 -->
      </template>
    </avue-form>

    <el-form-item style="margin-bottom: 0;">
      <el-button type="primary" icon="Check" @click="handleSubmit" :loading="submitLoading">提交</el-button>
      <el-button icon="Close" @click="handleCancel">关闭</el-button>
      <!-- 调试按钮 -->
      <el-button type="warning" @click="debugInfo" style="margin-left: 10px;">调试信息</el-button>
    </el-form-item>
  </basic-container>
</template>

<script setup>
  import { ref, getCurrentInstance, computed, toRefs, onMounted } from 'vue';
  import { useStore } from 'vuex';
  import histoicFlowVue from '@/views/work/process/mach/histoicFlow/histoicFlow.vue';
  import flowDiagramVue from '@/views/work/process/mach/flow-diagram/flow-diagram.vue';
  import mach from "@/utils/mach";
  import machPdf from "@/components/mach/pdf/mach-pdf.vue";
  import MachCard from "@/components/mach/card/mach-card.vue";
  import { auditFormDetail, audit } from "@/api/mach/pm/flowApvContractPayInfo/apvContractPayInfo";
  import apvContractPayInfoForm from "@/views/mach/pm/flowContract/flowApvContractPayInfo/apvContractPayInfo-form.vue";

  // 获取this
  let { proxy } = getCurrentInstance()

  // 属性
  const props = defineProps({
    taskId:{},
    processInstanceId:{},
    businessId:{},
    processDefinitionId:{},
    taskDefinitionKey:{},
    status:{},
    taskName:{},
    param:{},
  });

  const store = useStore();
  const userInfo = computed(() => store.state.user.userInfo);
  const permission = computed(() => store.state.user.permission);

  const flowTabSelected = ref("flow");
  const submitLoading = ref(false);
  const auditFormRef = ref(null);
  const payInfoFormRef = ref(null); // 添加表单ref
  const form = ref({});
  const auditOption = ref({});
  const auditForm = ref({
    comment: "",
    flag: "1"
  });

  // 审核提交
  async function handleSubmit() {
    console.log('开始提交驳回表单...');

    // 检查表单ref是否存在
    if (!payInfoFormRef.value) {
      console.error('payInfoFormRef 不存在');
      proxy.$Message.warning("表单未加载完成，请稍后再试");
      return false;
    }

    // 表单校验
    let formValidRes;
    try {
      formValidRes = await payInfoFormRef.value.validForm();
      console.log('表单校验结果:', formValidRes);
    } catch (error) {
      console.error('表单校验失败:', error);
      proxy.$Message.warning("表单校验失败");
      return false;
    }

    if (!formValidRes || !formValidRes.pass) {
      proxy.$Message.warning(formValidRes?.msg || "表单校验失败");
      return false;
    }

    // 审批意见校验
    if (!auditFormRef.value) {
      console.error('auditFormRef 不存在');
      proxy.$Message.warning("审批表单未加载完成，请稍后再试");
      return false;
    }

    let auditValidRes = await new Promise((resolve) => {
      auditFormRef.value.validate((valid, done) => {
        resolve(valid);
        done();
      });
    });

    if (!auditValidRes) {
      proxy.$Message.warning("请完成审批意见");
      return false;
    }

    // 获取表单数据并处理数组字段
    let payInfoData = JSON.parse(JSON.stringify(formValidRes.data));

    // 处理附件字段：如果是数组则转换为字符串
    if (Array.isArray(payInfoData.attachments)) {
        payInfoData.attachments = payInfoData.attachments.join(',');
    }

    // 处理请款单列表中的文件字段
    if (payInfoData.paymentRealList && Array.isArray(payInfoData.paymentRealList)) {
        payInfoData.paymentRealList = payInfoData.paymentRealList.map(item => ({
            ...item,
            // 确保文件字段是字符串格式
            contractFiles: Array.isArray(item.contractFiles)
                ? item.contractFiles.join(',')
                : (item.contractFiles || ''),
            attachmentFiles: Array.isArray(item.attachmentFiles)
                ? item.attachmentFiles.join(',')
                : (item.attachmentFiles || '')
        }));
    }

    // 获取流程定义Key - 从processDefinitionId中提取
    let processDefinitionKey = '';
    if (props.processDefinitionId) {
        // processDefinitionId格式通常是 "key:version:id"，我们需要提取key部分
        const parts = props.processDefinitionId.split(':');
        if (parts.length >= 1) {
            processDefinitionKey = parts[0];
        }
    }

    let params = Object.assign({}, payInfoData, {
      flow: {
        taskId: props.taskId,
        processInstanceId: props.processInstanceId,
        businessId: props.businessId,
        processDefinitionId: props.processDefinitionId,
        processDefinitionKey: processDefinitionKey, // 添加流程定义Key
        taskDefinitionKey: props.taskDefinitionKey,
        status: props.status,
        flag: auditForm.value.flag,
        comment: auditForm.value.comment,
      }
    });

    console.log('重新编辑提交参数:', params);
    console.log('流程定义Key:', processDefinitionKey);
    console.log('处理后的附件字段:', params.attachments);
    console.log('处理后的请款单列表:', params.paymentRealList);
    console.log('审批意见:', auditForm.value);

    submitLoading.value = true;

    try {
      const resp = await audit(params);
      console.log('提交成功响应:', resp);
      submitLoading.value = false;

      if (resp && resp.data) {
        proxy.$Message({message: resp.data.msg || "提交成功", type: "success"});
        handleCancel();
      } else {
        console.error('响应数据格式异常:', resp);
        proxy.$Message({message: "提交成功但响应格式异常", type: "warning"});
        handleCancel();
      }
    } catch (error) {
      console.error('重新编辑提交失败:', error);
      console.error('错误详情:', error.response || error.message);
      submitLoading.value = false;

      let errorMsg = "提交异常，请联系管理员";
      if (error.response && error.response.data && error.response.data.msg) {
        errorMsg = error.response.data.msg;
      } else if (error.message) {
        errorMsg = error.message;
      }

      proxy.$Message({message: errorMsg, type: "error"});
    }
  }

  // 调试信息
  function debugInfo() {
    console.log('=== 调试信息 ===');
    console.log('props:', props);
    console.log('form.value:', form.value);
    console.log('auditForm.value:', auditForm.value);
    console.log('payInfoFormRef.value:', payInfoFormRef.value);
    console.log('auditFormRef.value:', auditFormRef.value);
    console.log('submitLoading.value:', submitLoading.value);

    // 测试表单校验
    if (payInfoFormRef.value) {
      console.log('测试表单校验...');
      payInfoFormRef.value.validForm().then(res => {
        console.log('表单校验结果:', res);
      }).catch(err => {
        console.error('表单校验错误:', err);
      });
    } else {
      console.error('payInfoFormRef 不存在');
    }

    proxy.$Message.info('调试信息已输出到控制台');
  }

  // 关闭页面
  function handleCancel() {
    proxy.$router.$avueRouter.closeTag();
    proxy.$router.push({ path: `/work/tasks` });
  }

  function init() {
    // 获取审批表单配置
    auditOption.value = {
      submitBtn: false,  // 隐藏提交按钮
      emptyBtn: false,   // 隐藏清空按钮
      labelWidth: 120,   // 标签宽度
      column: [
        {
          label: "审批意见",
          prop: "comment",
          type: "textarea",
          span: 24,
          minRows: 3,
          maxRows: 5,
          rules: [
            {
              required: true,
              message: "请输入审批意见",
              trigger: "blur"
            }
          ]
        }
      ]
    };

    // 获取表单数据
    let flow = {
      taskId: props.taskId,
      processInstanceId: props.processInstanceId,
      businessId: props.businessId
    };

    auditFormDetail(flow).then(resp => {
      console.log('重新编辑页面获取到的数据:', resp.data.data);
      form.value = resp.data.data;

      // 检查数据结构
      if (!form.value.flowApvContractPayInfo) {
        console.error('重新编辑页面数据结构错误: 缺少 flowApvContractPayInfo 字段');
        proxy.$Message.error('数据加载失败：数据结构错误');
        return;
      }

      console.log('重新编辑页面表单数据设置完成:', form.value.flowApvContractPayInfo);

      // 强制触发视图更新
      proxy.$nextTick(() => {
        console.log('重新编辑页面视图更新完成');
        console.log('payInfoFormRef:', payInfoFormRef.value);
        console.log('auditFormRef:', auditFormRef.value);
      });
    }).catch(error => {
      console.error('重新编辑页面获取表单数据失败:', error);
      proxy.$Message.error('获取表单数据失败');
    });
  }

  onMounted(() => {
    init();
  });
</script>

<style lang="scss" scoped>
</style>
